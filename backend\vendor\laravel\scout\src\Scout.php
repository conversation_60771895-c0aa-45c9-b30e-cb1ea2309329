<?php

namespace <PERSON><PERSON>\Scout;

use <PERSON><PERSON>\Scout\Jobs\MakeSearchable;
use <PERSON><PERSON>\Scout\Jobs\RemoveFromSearch;

class Scout
{
    /**
     * The Scout library version.
     *
     * @var string
     */
    const VERSION = '10.17.0';

    /**
     * The job class that should make models searchable.
     *
     * @var string
     */
    public static $makeSearchableJob = MakeSearchable::class;

    /**
     * The job that should remove models from the search index.
     *
     * @var string
     */
    public static $removeFromSearchJob = RemoveFromSearch::class;

    /**
     * Specify the job class that should make models searchable.
     *
     * @param  string  $class
     * @return void
     */
    public static function makeSearchableUsing(string $class)
    {
        static::$makeSearchableJob = $class;
    }

    /**
     * Specify the job class that should remove models from the search index.
     *
     * @param  string  $class
     * @return void
     */
    public static function removeFromSearchUsing(string $class)
    {
        static::$removeFromSearchJob = $class;
    }
}
