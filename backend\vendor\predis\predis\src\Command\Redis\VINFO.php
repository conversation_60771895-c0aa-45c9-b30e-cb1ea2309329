<?php

/*
 * This file is part of the Predis package.
 *
 * (c) 2009-2020 <PERSON><PERSON> Alessandri
 * (c) 2021-2025 Till <PERSON>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Predis\Command\Redis;

use Predis\Command\Command as RedisCommand;
use Predis\Command\Redis\Utils\CommandUtility;

class VINFO extends RedisCommand
{
    /**
     * @return string
     */
    public function getId(): string
    {
        return 'VINFO';
    }

    /**
     * @param             $data
     * @return array|null
     */
    public function parseResponse($data): ?array
    {
        if (!is_null($data)) {
            return CommandUtility::arrayToDictionary($data);
        }

        return $data;
    }
}
