<?php

/*
 * This file is part of the Predis package.
 *
 * (c) 2009-2020 <PERSON><PERSON> Alessandri
 * (c) 2021-2025 Till <PERSON>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Predis\Command\Redis\Json;

use Predis\Command\PrefixableCommand as RedisCommand;

/**
 * @see https://redis.io/commands/json.arrtrim/
 *
 * Trim an array so that it contains only the specified inclusive range of elements
 */
class JSONARRTRIM extends RedisCommand
{
    public function getId()
    {
        return 'JSON.ARRTRIM';
    }

    public function prefixKeys($prefix)
    {
        $this->applyPrefixForFirstArgument($prefix);
    }
}
