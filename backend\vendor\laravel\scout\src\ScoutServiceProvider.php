<?php

namespace <PERSON><PERSON>\Scout;

use Illuminate\Support\ServiceProvider;
use <PERSON><PERSON>\Scout\Console\DeleteAllIndexesCommand;
use <PERSON><PERSON>\Scout\Console\DeleteIndexCommand;
use <PERSON><PERSON>\Scout\Console\FlushCommand;
use <PERSON><PERSON>\Scout\Console\ImportCommand;
use <PERSON><PERSON>\Scout\Console\IndexCommand;
use <PERSON><PERSON>\Scout\Console\QueueImportCommand;
use <PERSON><PERSON>\Scout\Console\SyncIndexSettingsCommand;
use Mei<PERSON>earch\Client as Meilisearch;

class ScoutServiceProvider extends ServiceProvider
{
    /**
     * Register the service provider.
     *
     * @return void
     */
    public function register()
    {
        $this->mergeConfigFrom(__DIR__.'/../config/scout.php', 'scout');

        if (class_exists(Meilisearch::class)) {
            $this->app->singleton(Meilisearch::class, function ($app) {
                $config = $app['config']->get('scout.meilisearch');

                return new Meilisearch(
                    $config['host'],
                    $config['key'],
                    clientAgents: [sprintf('Meilisearch Laravel Scout (v%s)', Scout::VERSION)],
                );
            });
        }

        $this->app->singleton(EngineManager::class, function ($app) {
            return new EngineManager($app);
        });
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        if ($this->app->runningInConsole()) {
            $this->commands([
                QueueImportCommand::class,
                FlushCommand::class,
                ImportCommand::class,
                IndexCommand::class,
                SyncIndexSettingsCommand::class,
                DeleteIndexCommand::class,
                DeleteAllIndexesCommand::class,
            ]);

            $this->publishes([
                __DIR__.'/../config/scout.php' => $this->app['path.config'].DIRECTORY_SEPARATOR.'scout.php',
            ]);
        }
    }
}
