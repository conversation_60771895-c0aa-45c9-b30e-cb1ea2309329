<?php

/*
 * This file is part of the Predis package.
 *
 * (c) 2009-2020 <PERSON><PERSON> Alessandri
 * (c) 2021-2025 Till <PERSON>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Predis\Command\Argument\Search;

class SugAddArguments extends CommonArguments
{
    /**
     * Adds INCR modifier.
     *
     * @return $this
     */
    public function incr(): self
    {
        $this->arguments[] = 'INCR';

        return $this;
    }
}
