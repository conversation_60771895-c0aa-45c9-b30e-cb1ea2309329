<?php

namespace Workbench\App\Providers;

use Illuminate\Support\ServiceProvider;
use <PERSON><PERSON>\Scout\EngineManager;
use <PERSON><PERSON>\Scout\Engines\NullEngine;
use <PERSON><PERSON>y as m;

class WorkbenchServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->singleton('scout.spied', function () {
            return m::spy(NullEngine::class);
        });

        $this->callAfterResolving(EngineManager::class, function ($engine) {
            $engine->extend('testing', function ($app) {
                return $app->make('scout.spied');
            });
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        //
    }
}
