<?php

/*
 * This file is part of the Predis package.
 *
 * (c) 2009-2020 <PERSON><PERSON> Alessandri
 * (c) 2021-2025 Till <PERSON>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Predis\Command\Redis\Search;

use Predis\Command\Command as RedisCommand;

/**
 * @see https://redis.io/commands/ft.sugdel/
 *
 * Delete a string from a suggestion index.
 */
class FTSUGDEL extends RedisCommand
{
    public function getId()
    {
        return 'FT.SUGDEL';
    }
}
