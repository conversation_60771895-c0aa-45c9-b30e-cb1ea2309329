<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\OpenAIService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

class AIController extends Controller
{
    private OpenAIService $openAIService;

    public function __construct(OpenAIService $openAIService)
    {
        $this->openAIService = $openAIService;
    }

    /**
     * Handle AI chat conversation
     */
    public function chat(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'message' => 'required|string|max:1000',
            'context' => 'array',
            'session_id' => 'string|max:255'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $message = $request->input('message');
            $context = $request->input('context', []);

            // Check if OpenAI is configured
            if (!$this->openAIService->isConfigured()) {
                return response()->json([
                    'success' => true,
                    'data' => [
                        'message' => "I understand you're looking for \"{$message}\". Let me search our products for you!",
                        'intent' => 'product_search',
                        'search_terms' => explode(' ', strtolower($message)),
                        'confidence' => 0.7
                    ],
                    'note' => 'AI service not configured, using fallback response'
                ]);
            }

            $result = $this->openAIService->processProductQuery($message, $context);

            if (!$result['success']) {
                return response()->json([
                    'success' => true,
                    'data' => $result['fallback'],
                    'note' => 'Using fallback due to AI service error'
                ]);
            }

            return response()->json([
                'success' => true,
                'data' => $result['data'],
                'usage' => $result['usage'] ?? null
            ]);

        } catch (\Exception $e) {
            Log::error('AI Chat Error: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'error' => 'An error occurred while processing your request'
            ], 500);
        }
    }

    /**
     * Handle AI-powered product search
     */
    public function search(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'query' => 'required|string|max:500',
            'filters' => 'array',
            'limit' => 'integer|min:1|max:50'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $query = $request->input('query');
            $filters = $request->input('filters', []);
            $limit = $request->input('limit', 20);

            // Process query with AI to extract search parameters
            $aiResult = $this->openAIService->processProductQuery($query, $filters);

            if ($aiResult['success']) {
                $searchParams = $aiResult['data'];
            } else {
                $searchParams = $aiResult['fallback'];
            }

            // TODO: Implement actual product search using the extracted parameters
            // For now, return the AI-processed search parameters

            return response()->json([
                'success' => true,
                'data' => [
                    'search_params' => $searchParams,
                    'products' => [], // TODO: Add actual product search results
                    'total' => 0,
                    'message' => $searchParams['message'] ?? 'Search completed'
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('AI Search Error: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'error' => 'Search service temporarily unavailable'
            ], 500);
        }
    }

    /**
     * Get AI-powered product recommendations
     */
    public function recommendations(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'user_preferences' => 'array',
            'product_ids' => 'array',
            'limit' => 'integer|min:1|max:20'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $preferences = $request->input('user_preferences', []);
            $productIds = $request->input('product_ids', []);
            $limit = $request->input('limit', 5);

            // TODO: Fetch actual products from database
            $products = []; // Placeholder for actual product data

            $result = $this->openAIService->generateRecommendations($preferences, $products);

            return response()->json([
                'success' => $result['success'],
                'data' => [
                    'recommendations' => $result['recommendations'] ?? [],
                    'explanation' => $result['explanation'] ?? '',
                    'limit' => $limit
                ],
                'error' => $result['error'] ?? null
            ]);

        } catch (\Exception $e) {
            Log::error('AI Recommendations Error: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'error' => 'Recommendation service temporarily unavailable'
            ], 500);
        }
    }
}
