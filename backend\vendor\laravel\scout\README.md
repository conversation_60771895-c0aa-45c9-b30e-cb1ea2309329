<p align="center"><img width="316" height="81" src="/art/logo.svg" alt="Logo Laravel Scout"></p>

<p align="center">
<a href="https://github.com/laravel/scout/actions"><img src="https://github.com/laravel/scout/workflows/tests/badge.svg" alt="Build Status"></a>
<a href="https://packagist.org/packages/laravel/scout"><img src="https://img.shields.io/packagist/dt/laravel/scout" alt="Total Downloads"></a>
<a href="https://packagist.org/packages/laravel/scout"><img src="https://img.shields.io/packagist/v/laravel/scout" alt="Latest Stable Version"></a>
<a href="https://packagist.org/packages/laravel/scout"><img src="https://img.shields.io/packagist/l/laravel/scout" alt="License"></a>
</p>

## Introduction

Laravel Scout provides a simple, driver-based solution for adding full-text search to your Eloquent models. Once Scout is installed and configured, it will automatically sync your model changes to your search indexes. Currently, Scout supports:

- [Algolia](https://www.algolia.com/)
- [Meilisearch](https://github.com/meilisearch/meilisearch)
- [Typesense](https://github.com/typesense/typesense)

## Official Documentation

Documentation for Scout can be found on the [Laravel website](https://laravel.com/docs/master/scout).

## Contributing

Thank you for considering contributing to Scout! The contribution guide can be found in the [Laravel documentation](https://laravel.com/docs/contributions).

## Code of Conduct

In order to ensure that the Laravel community is welcoming to all, please review and abide by the [Code of Conduct](https://laravel.com/docs/contributions#code-of-conduct).

## Security Vulnerabilities

Please review [our security policy](https://github.com/laravel/scout/security/policy) on how to report security vulnerabilities.

## License

Laravel Scout is open-sourced software licensed under the [MIT license](LICENSE.md).
