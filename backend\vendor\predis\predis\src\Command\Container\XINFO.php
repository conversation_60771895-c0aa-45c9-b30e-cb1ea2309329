<?php

/*
 * This file is part of the Predis package.
 *
 * (c) 2009-2020 <PERSON><PERSON> Alessandri
 * (c) 2021-2025 Till <PERSON>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Predis\Command\Container;

use Predis\Command\Argument\Stream\XInfoStreamOptions;

/**
 * @method array consumers(string $key, string $group)
 * @method array groups(string $key)
 * @method array stream(string $key, XInfoStreamOptions $options = null)
 */
class XINFO extends AbstractContainer
{
    public function getContainerCommandId(): string
    {
        return 'XINFO';
    }
}
