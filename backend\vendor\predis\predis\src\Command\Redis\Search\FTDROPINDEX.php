<?php

/*
 * This file is part of the Predis package.
 *
 * (c) 2009-2020 <PERSON><PERSON> Alessandri
 * (c) 2021-2025 Till <PERSON>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Predis\Command\Redis\Search;

use Predis\Command\PrefixableCommand as RedisCommand;

class FTDROPINDEX extends RedisCommand
{
    public function getId()
    {
        return 'FT.DROPINDEX';
    }

    public function setArguments(array $arguments)
    {
        [$index] = $arguments;
        $commandArguments = [];

        if (!empty($arguments[1])) {
            $commandArguments = $arguments[1]->toArray();
        }

        parent::setArguments(array_merge(
            [$index],
            $commandArguments
        ));
    }

    public function prefixKeys($prefix)
    {
        $this->applyPrefixForFirstArgument($prefix);
    }
}
