<?xml version="1.0" encoding="UTF-8"?>
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="https://schema.phpunit.de/10.1/phpunit.xsd"
         bootstrap="vendor/autoload.php"
         convertErrorsToExceptions="true"
         convertNoticesToExceptions="true"
         convertWarningsToExceptions="true"
         stopOnFailure="false"
         cacheResult="false"
         colors="true">
    <testsuites>
        <testsuite name="Tests">
            <directory suffix="Test.php">./tests</directory>
        </testsuite>
    </testsuites>
    <coverage>
        <include>
            <directory>src/</directory>
        </include>
    </coverage>
    <php>
        <env name="MEILISEARCH_URL" value="http://localhost:7700"/>
        <env name="MEILISEARCH_API_KEY" value="masterKey"/>
    </php>
</phpunit>
