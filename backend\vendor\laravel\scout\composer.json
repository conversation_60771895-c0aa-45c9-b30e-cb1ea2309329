{"name": "laravel/scout", "description": "Laravel Scout provides a driver based solution to searching your Eloquent models.", "keywords": ["algolia", "laravel", "search"], "license": "MIT", "support": {"issues": "https://github.com/laravel/scout/issues", "source": "https://github.com/laravel/scout"}, "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": "^8.0", "illuminate/bus": "^9.0|^10.0|^11.0|^12.0", "illuminate/contracts": "^9.0|^10.0|^11.0|^12.0", "illuminate/database": "^9.0|^10.0|^11.0|^12.0", "illuminate/http": "^9.0|^10.0|^11.0|^12.0", "illuminate/pagination": "^9.0|^10.0|^11.0|^12.0", "illuminate/queue": "^9.0|^10.0|^11.0|^12.0", "illuminate/support": "^9.0|^10.0|^11.0|^12.0", "symfony/console": "^6.0|^7.0"}, "require-dev": {"algolia/algoliasearch-client-php": "^3.2|^4.0", "typesense/typesense-php": "^4.9.3", "meilisearch/meilisearch-php": "^1.0", "mockery/mockery": "^1.0", "orchestra/testbench": "^7.31|^8.11|^9.0|^10.0", "php-http/guzzle7-adapter": "^1.0", "phpstan/phpstan": "^1.10", "phpunit/phpunit": "^9.3|^10.4|^11.5"}, "conflict": {"algolia/algoliasearch-client-php": "<3.2.0|>=5.0.0"}, "autoload": {"psr-4": {"Laravel\\Scout\\": "src/"}}, "autoload-dev": {"psr-4": {"Laravel\\Scout\\Tests\\": "tests/", "Workbench\\App\\": "workbench/app/", "Workbench\\Database\\Factories\\": "workbench/database/factories/"}}, "extra": {"branch-alias": {"dev-master": "10.x-dev"}, "laravel": {"providers": ["Laravel\\Scout\\ScoutServiceProvider"]}}, "suggest": {"algolia/algoliasearch-client-php": "Required to use the Algolia engine (^3.2).", "meilisearch/meilisearch-php": "Required to use the Meilisearch engine (^1.0).", "typesense/typesense-php": "Required to use the Typesense engine (^4.9)."}, "config": {"sort-packages": true, "allow-plugins": {"php-http/discovery": true}}, "minimum-stability": "dev", "prefer-stable": true}