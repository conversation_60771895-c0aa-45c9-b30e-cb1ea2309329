<?php

namespace App\Services;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class OpenAIService
{
    private Client $client;
    private string $apiKey;
    private string $model;
    private int $maxTokens;

    public function __construct()
    {
        $this->client = new Client([
            'base_uri' => 'https://api.openai.com/v1/',
            'timeout' => 30,
        ]);
        
        $this->apiKey = config('services.openai.api_key');
        $this->model = config('services.openai.model', 'gpt-4');
        $this->maxTokens = config('services.openai.max_tokens', 1000);
    }

    /**
     * Process a user query for product search
     */
    public function processProductQuery(string $query, array $context = []): array
    {
        try {
            $prompt = $this->buildProductSearchPrompt($query, $context);
            
            $response = $this->client->post('chat/completions', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $this->apiKey,
                    'Content-Type' => 'application/json',
                ],
                'json' => [
                    'model' => $this->model,
                    'messages' => [
                        [
                            'role' => 'system',
                            'content' => 'You are CartlyAI, an expert shopping assistant. Help users find products by understanding their needs and preferences. Always respond in JSON format with search parameters and a friendly message.'
                        ],
                        [
                            'role' => 'user',
                            'content' => $prompt
                        ]
                    ],
                    'max_tokens' => $this->maxTokens,
                    'temperature' => 0.7,
                    'response_format' => ['type' => 'json_object']
                ]
            ]);

            $data = json_decode($response->getBody()->getContents(), true);
            $content = json_decode($data['choices'][0]['message']['content'], true);

            return [
                'success' => true,
                'data' => $content,
                'usage' => $data['usage'] ?? null
            ];

        } catch (RequestException $e) {
            Log::error('OpenAI API Error: ' . $e->getMessage());
            
            return [
                'success' => false,
                'error' => 'AI service temporarily unavailable',
                'fallback' => $this->getFallbackResponse($query)
            ];
        }
    }

    /**
     * Generate product recommendations based on user preferences
     */
    public function generateRecommendations(array $userPreferences, array $products): array
    {
        $cacheKey = 'ai_recommendations_' . md5(json_encode($userPreferences));
        
        return Cache::remember($cacheKey, 300, function () use ($userPreferences, $products) {
            try {
                $prompt = $this->buildRecommendationPrompt($userPreferences, $products);
                
                $response = $this->client->post('chat/completions', [
                    'headers' => [
                        'Authorization' => 'Bearer ' . $this->apiKey,
                        'Content-Type' => 'application/json',
                    ],
                    'json' => [
                        'model' => $this->model,
                        'messages' => [
                            [
                                'role' => 'system',
                                'content' => 'You are a product recommendation expert. Analyze user preferences and product data to provide personalized recommendations with explanations.'
                            ],
                            [
                                'role' => 'user',
                                'content' => $prompt
                            ]
                        ],
                        'max_tokens' => $this->maxTokens,
                        'temperature' => 0.8,
                        'response_format' => ['type' => 'json_object']
                    ]
                ]);

                $data = json_decode($response->getBody()->getContents(), true);
                $content = json_decode($data['choices'][0]['message']['content'], true);

                return [
                    'success' => true,
                    'recommendations' => $content['recommendations'] ?? [],
                    'explanation' => $content['explanation'] ?? ''
                ];

            } catch (RequestException $e) {
                Log::error('OpenAI Recommendations Error: ' . $e->getMessage());
                
                return [
                    'success' => false,
                    'error' => 'Recommendation service unavailable',
                    'recommendations' => []
                ];
            }
        });
    }

    /**
     * Build prompt for product search
     */
    private function buildProductSearchPrompt(string $query, array $context): string
    {
        $contextStr = !empty($context) ? "\nContext: " . json_encode($context) : '';
        
        return "User query: \"{$query}\"{$contextStr}

Please analyze this query and extract search parameters. Return a JSON response with:
- search_terms: array of relevant keywords
- category: suggested product category
- price_range: {min: number, max: number} if mentioned
- features: array of desired features
- intent: the user's shopping intent
- message: a friendly response to the user
- confidence: confidence score (0-1)

Example response:
{
    \"search_terms\": [\"wireless\", \"headphones\", \"noise cancelling\"],
    \"category\": \"electronics\",
    \"price_range\": {\"min\": 0, \"max\": 200},
    \"features\": [\"noise cancelling\", \"wireless\", \"bluetooth\"],
    \"intent\": \"product_search\",
    \"message\": \"I'll help you find great noise-cancelling headphones under $200!\",
    \"confidence\": 0.9
}";
    }

    /**
     * Build prompt for recommendations
     */
    private function buildRecommendationPrompt(array $preferences, array $products): string
    {
        $preferencesStr = json_encode($preferences);
        $productsStr = json_encode(array_slice($products, 0, 10)); // Limit to 10 products
        
        return "User preferences: {$preferencesStr}

Available products: {$productsStr}

Based on the user's preferences and the available products, recommend the top 3 products with explanations. Return JSON:
{
    \"recommendations\": [
        {
            \"product_id\": 1,
            \"score\": 0.95,
            \"reason\": \"Perfect match for your needs because...\"
        }
    ],
    \"explanation\": \"I chose these products because...\"
}";
    }

    /**
     * Get fallback response when AI is unavailable
     */
    private function getFallbackResponse(string $query): array
    {
        // Simple keyword extraction as fallback
        $keywords = array_filter(explode(' ', strtolower($query)));
        
        return [
            'search_terms' => $keywords,
            'category' => null,
            'price_range' => null,
            'features' => [],
            'intent' => 'product_search',
            'message' => "I'll search for products related to your query.",
            'confidence' => 0.5
        ];
    }

    /**
     * Check if OpenAI service is configured
     */
    public function isConfigured(): bool
    {
        return !empty($this->apiKey) && $this->apiKey !== 'your_openai_api_key_here';
    }
}
